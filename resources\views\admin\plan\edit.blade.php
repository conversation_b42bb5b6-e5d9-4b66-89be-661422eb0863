@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Edit Plan'])

    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header pb-0 d-flex justify-content-between align-items-center">
                        <h6>Edit Plan: {{ $plan->name }}</h6>
                        <a href="{{ route('plan.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i> Back to Plans
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('plan.update', $plan->plans_id) }}" method="POST">
                            @csrf
                            @method('PUT')
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name" class="form-control-label">Plan Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                               id="name" name="name" value="{{ old('name', $plan->name) }}" 
                                               placeholder="Enter plan name" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="price" class="form-control-label">Price <span class="text-danger">*</span></label>
                                        <input type="number" step="0.01" min="0" 
                                               class="form-control @error('price') is-invalid @enderror" 
                                               id="price" name="price" value="{{ old('price', $plan->price) }}" 
                                               placeholder="Enter price" required>
                                        @error('price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="duration" class="form-control-label">Duration <span class="text-danger">*</span></label>
                                        <select class="form-control @error('duration') is-invalid @enderror" 
                                                id="duration" name="duration" required>
                                            <option value="">Select Duration</option>
                                            <option value="weekly" {{ old('duration', $plan->duration) == 'weekly' ? 'selected' : '' }}>Weekly</option>
                                            <option value="monthly" {{ old('duration', $plan->duration) == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                            <option value="yearly" {{ old('duration', $plan->duration) == 'yearly' ? 'selected' : '' }}>Yearly</option>
                                        </select>
                                        @error('duration')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sort_order" class="form-control-label">Sort Order</label>
                                        <input type="number" min="0" 
                                               class="form-control @error('sort_order') is-invalid @enderror" 
                                               id="sort_order" name="sort_order" value="{{ old('sort_order', $plan->sort_order) }}" 
                                               placeholder="Enter sort order">
                                        @error('sort_order')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="description" class="form-control-label">Description</label>
                                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                                  id="description" name="description" rows="4" 
                                                  placeholder="Enter plan description">{{ old('description', $plan->description) }}</textarea>
                                        @error('description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-control-label">Features</label>
                                        <div id="features-container">
                                            @php
                                                $features = old('features', $plan->features ?? []);
                                            @endphp
                                            @if(!empty($features))
                                                @foreach($features as $index => $feature)
                                                    <div class="input-group mb-2 feature-item">
                                                        <input type="text" class="form-control" name="features[]"
                                                               value="{{ $feature }}" placeholder="Enter feature">
                                                        <button type="button" class="btn btn-outline-danger remove-feature">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                @endforeach
                                            @else
                                                <div class="input-group mb-2 feature-item">
                                                    <input type="text" class="form-control" name="features[]"
                                                           placeholder="Enter feature">
                                                    <button type="button" class="btn btn-outline-danger remove-feature">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            @endif
                                        </div>
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="add-feature">
                                            <i class="fas fa-plus me-1"></i> Add Feature
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-control-label">Listed Services</label>
                                        <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                            @if($services->count() > 0)
                                                @php
                                                    $selectedServices = old('services', $plan->services->pluck('service_id')->toArray());
                                                @endphp
                                                @foreach($services as $service)
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox"
                                                               name="services[]" value="{{ $service->service_id }}"
                                                               id="service_{{ $service->service_id }}"
                                                               {{ in_array($service->service_id, $selectedServices) ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="service_{{ $service->service_id }}">
                                                            {{ $service->name }}
                                                            @if($service->description)
                                                                <small class="text-muted d-block">{{ Str::limit($service->description, 50) }}</small>
                                                            @endif
                                                        </label>
                                                    </div>
                                                @endforeach
                                            @else
                                                <p class="text-muted mb-0">No services available. <a href="{{ route('service.create') }}" target="_blank">Create services first</a>.</p>
                                            @endif
                                        </div>
                                        <small class="form-text text-muted">Select services that will be included in this plan</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="status" class="form-control-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-control @error('status') is-invalid @enderror" 
                                                id="status" name="status" required>
                                            <option value="1" {{ old('status', $plan->status) == '1' ? 'selected' : '' }}>Active</option>
                                            <option value="0" {{ old('status', $plan->status) == '0' ? 'selected' : '' }}>Inactive</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="is_popular" class="form-control-label">Popular Plan</label>
                                        <select class="form-control @error('is_popular') is-invalid @enderror" 
                                                id="is_popular" name="is_popular">
                                            <option value="0" {{ old('is_popular', $plan->is_popular) == '0' ? 'selected' : '' }}>No</option>
                                            <option value="1" {{ old('is_popular', $plan->is_popular) == '1' ? 'selected' : '' }}>Yes</option>
                                        </select>
                                        @error('is_popular')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i> Update Plan
                                        </button>
                                        <a href="{{ route('plan.index') }}" class="btn btn-secondary ms-2">
                                            <i class="fas fa-times me-1"></i> Cancel
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    // Add new feature input
    $('#add-feature').click(function() {
        var featureHtml = `
            <div class="input-group mb-2 feature-item">
                <input type="text" class="form-control" name="features[]" placeholder="Enter feature">
                <button type="button" class="btn btn-outline-danger remove-feature">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        $('#features-container').append(featureHtml);
    });

    // Remove feature input
    $(document).on('click', '.remove-feature', function() {
        if ($('.feature-item').length > 1) {
            $(this).closest('.feature-item').remove();
        } else {
            toastr.warning('At least one feature field is required');
        }
    });
});
</script>
@endpush
