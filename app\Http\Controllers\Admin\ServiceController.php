<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Service;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class ServiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $services = Service::select('service_id', 'name', 'description', 'status', 'sort_order', 'created_at')
                    ->orderBy('sort_order', 'asc')
                    ->orderByDesc('created_at')
                    ->get();

                return DataTables::of($services)
                    ->addIndexColumn()
                    ->editColumn('name', fn($s) => ucwords($s->name ?? '-'))
                    ->editColumn('description', fn($s) => Str::limit($s->description ?? '-', 50))
                    ->editColumn('sort_order', fn($s) => $s->sort_order ?? 0)
                    ->editColumn('status', function ($s) {
                        $status = $s->status ? 'Active' : 'Inactive';
                        $btnClass = $s->status ? 'btn-success' : 'btn-secondary';
                        return '<button type="button" class="m-0 bdr btn btn-sm toggle-status ' . $btnClass . '" data-id="' . e($s->service_id) . '">' . $status . '</button>';
                    })
                    ->editColumn('created_at', fn($s) => optional($s->created_at)->format('d M, Y h:i A'))
                    ->addColumn('actions', function ($s) {
                        $id = e($s->service_id);
                        $editUrl = route('service.edit', $id);
                        return '
                    <div class="d-flex gap-2">
                        <a href="' . $editUrl . '" class="text-primary ttt" title="Edit"><i class="fas fa-edit"></i></a>
                        <button type="button" class="text-danger border-0 bg-transparent p-0 m-0 delete-button ttt" data-id="' . $id . '" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>';
                    })
                    ->rawColumns(['status', 'actions'])
                    ->make(true);
            }

            return view('admin.service.index');
        } catch (Exception $e) {
            Log::error('Service index error: ' . $e->getMessage());
            return redirect()->back()->withErrors(['error' => 'Failed to load services.']);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.service.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|min:3',
                'description' => 'nullable|string|max:1000',
                'status' => 'required|in:0,1',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            $service = new Service([
                'service_id' => (string) Str::uuid(),
                'name' => $validated['name'],
                'description' => $validated['description'],
                'status' => $validated['status'] ?? 1,
                'sort_order' => $validated['sort_order'] ?? 0,
            ]);

            $service->save();

            return redirect()->route('service.index')->with('success', 'Service created successfully!');
        } catch (Exception $e) {
            Log::error('Service store error: ' . $e->getMessage());
            return redirect()->back()->withErrors(['error' => 'Failed to create service. Please try again.'])->withInput();
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $service = Service::where('service_id', $id)->firstOrFail();
        return view('admin.service.edit', compact('service'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $service = Service::where('service_id', $id)->firstOrFail();

            $validated = $request->validate([
                'name' => 'required|string|max:255|min:3',
                'description' => 'nullable|string|max:1000',
                'status' => 'required|in:0,1',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            $service->update([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'status' => $validated['status'],
                'sort_order' => $validated['sort_order'] ?? 0,
            ]);

            return redirect()->route('service.index')->with('success', 'Service updated successfully!');
        } catch (Exception $e) {
            Log::error('Service update error: ' . $e->getMessage());
            return redirect()->back()->withErrors(['error' => 'Failed to update service. Please try again.'])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $service = Service::where('service_id', $id)->firstOrFail();
            $service->delete();

            return response()->json(['status' => true, 'message' => 'Service deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Service delete error: ' . $e->getMessage());
            return response()->json(['status' => false, 'message' => 'Failed to delete service.'], 500);
        }
    }

    /**
     * Toggle service status
     */
    public function status($id)
    {
        try {
            $service = Service::where('service_id', $id)->firstOrFail();
            $service->status = !$service->status;
            $service->save();

            return response()->json([
                'status' => true,
                'newStatus' => $service->status ? 'Active' : 'Inactive'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to toggle service status: ' . $e->getMessage());
            return response()->json(['status' => false], 500);
        }
    }
}
