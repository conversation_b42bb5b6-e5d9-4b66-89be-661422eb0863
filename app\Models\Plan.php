<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Plan extends Model
{
    use HasFactory;

    protected $primaryKey = 'plans_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'description',
        'price',
        'duration',
        'features',
        'status',
        'is_popular',
        'sort_order',
    ];

    protected $casts = [
        'features' => 'array',
        'status' => 'boolean',
        'is_popular' => 'boolean',
        'price' => 'decimal:2',
    ];

    protected static function booted(): void
    {
        static::creating(function ($plan) {
            if (empty($plan->plans_id)) {
                $plan->plans_id = (string) Str::uuid();
            }
        });
    }

    // Scope for active plans
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    // Scope for popular plans
    public function scopePopular($query)
    {
        return $query->where('is_popular', 1);
    }

    // Relationship with services
    public function services()
    {
        return $this->belongsToMany(Service::class, 'plan_services', 'plan_id', 'service_id');
    }
}
