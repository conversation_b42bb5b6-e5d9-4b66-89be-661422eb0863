@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Services'])

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12">
                <div class="card mb-4">
                    <div class="d-flex justify-content-between px-3 py-4">
                        <h6 class="mb-0 ttl">Services</h6>
                        <a href="{{ route('service.create') }}" class="btn btn-sm m-0 bdr">
                            <i class="fa fa-plus me-1"></i> Add Service
                        </a>
                    </div>

                    <div class="card-body p-3">
                        <x-data-table id="servicesTable" :ajax="route('service.index')" :columns="[
                            [
                                'data' => 'DT_RowIndex',
                                'name' => 'DT_RowIndex',
                                'orderable' => false,
                                'searchable' => false,
                            ],
                            ['data' => 'name', 'name' => 'name'],
                            ['data' => 'description', 'name' => 'description'],
                            ['data' => 'sort_order', 'name' => 'sort_order'],
                            ['data' => 'status', 'name' => 'status'],
                            ['data' => 'created_at', 'name' => 'created_at'],
                            ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                        ]" :order="[]">
                            <x-slot:header>
                                <th>S.No.</th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Sort Order</th>
                                <th>Status</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </x-slot:header>
                        </x-data-table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Toggle status
            $(document).on('click', '.toggle-status', function() {
                var serviceId = $(this).data('id');
                var button = $(this);
                
                $.ajax({
                    url: '{{ route("service.status", ":id") }}'.replace(':id', serviceId),
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.status) {
                            button.text(response.newStatus);
                            button.removeClass('btn-success btn-secondary');
                            button.addClass(response.newStatus === 'Active' ? 'btn-success' : 'btn-secondary');
                            toastr.success('Status updated successfully');
                        }
                    },
                    error: function() {
                        toastr.error('Failed to update status');
                    }
                });
            });

            // Delete service
            $(document).on('click', '.delete-button', function() {
                var serviceId = $(this).data('id');
                
                if (confirm('Are you sure you want to delete this service?')) {
                    $.ajax({
                        url: '{{ route("service.destroy", ":id") }}'.replace(':id', serviceId),
                        type: 'DELETE',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.status) {
                                $('#servicesTable').DataTable().ajax.reload();
                                toastr.success(response.message);
                            } else {
                                toastr.error(response.message);
                            }
                        },
                        error: function() {
                            toastr.error('Failed to delete service');
                        }
                    });
                }
            });
        });
    </script>
@endsection
