<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Plan Details'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header pb-0 d-flex justify-content-between align-items-center">
                        <h6>Plan Details: <?php echo e($plan->name); ?></h6>
                        <div>
                            <a href="<?php echo e(route('plan.edit', $plan->plans_id)); ?>" class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-edit me-1"></i> Edit Plan
                            </a>
                            <a href="<?php echo e(route('plan.index')); ?>" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i> Back to Plans
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Plan Name:</label>
                                    <p class="text-muted mb-0"><?php echo e($plan->name); ?></p>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Price:</label>
                                    <p class="text-muted mb-0">$<?php echo e(number_format($plan->price, 2)); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Duration:</label>
                                    <p class="text-muted mb-0"><?php echo e(ucfirst($plan->duration)); ?></p>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Sort Order:</label>
                                    <p class="text-muted mb-0"><?php echo e($plan->sort_order); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Status:</label>
                                    <p class="mb-0">
                                        <?php if($plan->status): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Popular Plan:</label>
                                    <p class="mb-0">
                                        <?php if($plan->is_popular): ?>
                                            <span class="badge bg-warning">Yes</span>
                                        <?php else: ?>
                                            <span class="badge bg-light text-dark">No</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <?php if($plan->description): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Description:</label>
                                    <p class="text-muted mb-0"><?php echo e($plan->description); ?></p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if($plan->features && count($plan->features) > 0): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Features:</label>
                                    <ul class="list-unstyled mt-2">
                                        <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="mb-1">
                                                <i class="fas fa-check text-success me-2"></i>
                                                <?php echo e($feature); ?>

                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Created At:</label>
                                    <p class="text-muted mb-0"><?php echo e($plan->created_at->format('d M, Y h:i A')); ?></p>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="form-control-label fw-bold">Last Updated:</label>
                                    <p class="text-muted mb-0"><?php echo e($plan->updated_at->format('d M, Y h:i A')); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="<?php echo e(route('plan.edit', $plan->plans_id)); ?>" class="btn btn-primary me-2">
                                        <i class="fas fa-edit me-1"></i> Edit Plan
                                    </a>
                                    <button type="button" class="btn btn-danger me-2" onclick="deletePlan('<?php echo e($plan->plans_id); ?>')">
                                        <i class="fas fa-trash me-1"></i> Delete Plan
                                    </button>
                                    <a href="<?php echo e(route('plan.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i> Back to Plans
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
function deletePlan(planId) {
    if (confirm('Are you sure you want to delete this plan?')) {
        $.ajax({
            url: "<?php echo e(route('plan.destroy', ':id')); ?>".replace(':id', planId),
            type: 'DELETE',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.success);
                    setTimeout(function() {
                        window.location.href = "<?php echo e(route('plan.index')); ?>";
                    }, 1500);
                } else {
                    toastr.error(response.error || 'Something went wrong');
                }
            },
            error: function(xhr) {
                var errorMsg = xhr.responseJSON?.error || 'Something went wrong';
                toastr.error(errorMsg);
            }
        });
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/plan/show.blade.php ENDPATH**/ ?>